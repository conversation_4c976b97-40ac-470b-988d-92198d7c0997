// SigilAttributeSet.cpp
// AURACRON - Sistema de Sígilos
// Implementação do conjunto de atributos espectrais para GameplayAbilities System UE 5.6
// APIs verificadas: AttributeSet.h, AbilitySystemComponent.h, GameplayEffectExtension.h

#include "Sigils/SigilAttributeSet.h"
#include "GameplayEffect.h"
#include "GameplayEffectExtension.h"
#include "Net/UnrealNetwork.h"
#include "Engine/Engine.h"
#include "GameFramework/Character.h"
#include "GameFramework/PlayerController.h"
#include "AbilitySystemBlueprintLibrary.h"
#include "GameplayAbilitySpec.h"

// Log category específica para o sistema de sigilos
DEFINE_LOG_CATEGORY_STATIC(LogSigilAttributeSet, Log, All);

USigilAttributeSet::USigilAttributeSet()
{
    // Inicializar atributos primários espectrais com valores base
    SpectralPower = 10.0f;
    SpectralResilience = 10.0f;
    SpectralVelocity = 10.0f;
    SpectralFocus = 10.0f;

    // Inicializar atributos derivados (serão recalculados)
    AttackPower = 25.0f;  // 10 + (10 * 1.5)
    DefensePower = 22.0f; // 10 + (10 * 1.2)
    AttackSpeed = 1.1f;   // 1.0 + (10 * 0.01)
    CriticalChance = 0.02f; // (10 + 10) * 0.001
    CriticalMultiplier = 150.2f; // 150 + (10 * 0.002)

    // Inicializar atributos de mobilidade
    MovementSpeed = 320.0f; // 300 + (10 * 2.0)
    CooldownReduction = 0.1f; // 10 * 0.01

    // Inicializar atributos de recursos
    ManaRegeneration = 5.0f; // 10 * 0.5
    HealthRegeneration = 3.0f; // 10 * 0.3

    // Inicializar atributos de estado
    FusionMultiplier = 1.0f;
    SigilSlots = 3.0f; // Começa com 3 slots
    SigilEfficiency = 1.0f;
    SigilExperience = 0.0f;

    // Inicializar atributos MOBA
    TeamFightBonus = 0.0f;
    ObjectiveBonus = 0.0f;
    CCResistance = 0.0f;

    // Marcar cache como sujo
    bTotalPowerCacheDirty = true;
}

void USigilAttributeSet::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    // Replicar atributos primários espectrais
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAttributeSet, SpectralPower, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAttributeSet, SpectralResilience, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAttributeSet, SpectralVelocity, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAttributeSet, SpectralFocus, COND_None, REPNOTIFY_Always);

    // Replicar atributos derivados de combate
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAttributeSet, AttackPower, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAttributeSet, DefensePower, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAttributeSet, AttackSpeed, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAttributeSet, CriticalChance, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAttributeSet, CriticalMultiplier, COND_None, REPNOTIFY_Always);

    // Replicar atributos de mobilidade
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAttributeSet, MovementSpeed, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAttributeSet, CooldownReduction, COND_None, REPNOTIFY_Always);

    // Replicar atributos de recursos
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAttributeSet, ManaRegeneration, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAttributeSet, HealthRegeneration, COND_None, REPNOTIFY_Always);

    // Replicar atributos de estado
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAttributeSet, FusionMultiplier, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAttributeSet, SigilSlots, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAttributeSet, SigilEfficiency, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAttributeSet, SigilExperience, COND_None, REPNOTIFY_Always);

    // Replicar atributos MOBA
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAttributeSet, TeamFightBonus, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAttributeSet, ObjectiveBonus, COND_None, REPNOTIFY_Always);
    DOREPLIFETIME_CONDITION_NOTIFY(USigilAttributeSet, CCResistance, COND_None, REPNOTIFY_Always);
}

void USigilAttributeSet::PreAttributeChange(const FGameplayAttribute& Attribute, float& NewValue)
{
    Super::PreAttributeChange(Attribute, NewValue);

    // Aplicar limites aos atributos antes da mudança
    ClampAttributeValue(Attribute, NewValue);

    // Marcar cache como sujo se atributo primário mudou
    if (Attribute == GetSpectralPowerAttribute() ||
        Attribute == GetSpectralResilienceAttribute() ||
        Attribute == GetSpectralVelocityAttribute() ||
        Attribute == GetSpectralFocusAttribute())
    {
        bTotalPowerCacheDirty = true;
    }
}

void USigilAttributeSet::PostGameplayEffectExecute(const FGameplayEffectModCallbackData& Data)
{
    Super::PostGameplayEffectExecute(Data);

    // Verificações de segurança para ponteiros
    if (!Data.EffectSpec.IsValid())
    {
        UE_LOG(LogSigilAttributeSet, Warning, TEXT("PostGameplayEffectExecute: Invalid EffectSpec"));
        return;
    }

    FGameplayEffectContextHandle Context = Data.EffectSpec.GetContext();
    UAbilitySystemComponent* Source = Context.GetOriginalInstigatorAbilitySystemComponent();
    const FGameplayTagContainer& SourceTags = *Data.EffectSpec.CapturedSourceTags.GetAggregatedTags();
    const FGameplayTagContainer& TargetTags = *Data.EffectSpec.CapturedTargetTags.GetAggregatedTags();

    // Obter magnitude da mudança
    float DeltaValue = 0;
    if (Data.EvaluatedData.ModifierOp == EGameplayModOp::Type::Additive)
    {
        DeltaValue = Data.EvaluatedData.Magnitude;
    }

    // Processar mudanças em atributos específicos
    FGameplayAttribute Attribute = Data.EvaluatedData.Attribute;

    // Se atributo primário mudou, recalcular derivados
    if (Attribute == GetSpectralPowerAttribute() ||
        Attribute == GetSpectralResilienceAttribute() ||
        Attribute == GetSpectralVelocityAttribute() ||
        Attribute == GetSpectralFocusAttribute())
    {
        RecalculateDerivedAttributes();
    }

    // Aplicar multiplicador de fusão se ativo
    if (GetFusionMultiplier() > 1.0f)
    {
        // Aplicar multiplicador apenas a atributos de combate
        if (Attribute == GetAttackPowerAttribute() ||
            Attribute == GetDefensePowerAttribute() ||
            Attribute == GetAttackSpeedAttribute())
        {
            float CurrentValue = 0.0f;
            if (Attribute == GetAttackPowerAttribute()) CurrentValue = AttackPower.GetCurrentValue();
            else if (Attribute == GetDefensePowerAttribute()) CurrentValue = DefensePower.GetCurrentValue();
            else if (Attribute == GetAttackSpeedAttribute()) CurrentValue = AttackSpeed.GetCurrentValue();
            
            float NewValue = CurrentValue * GetFusionMultiplier();
            
            if (Attribute == GetAttackPowerAttribute()) AttackPower.SetBaseValue(NewValue);
            else if (Attribute == GetDefensePowerAttribute()) DefensePower.SetBaseValue(NewValue);
            else if (Attribute == GetAttackSpeedAttribute()) AttackSpeed.SetBaseValue(NewValue);
        }
    }

    // Notificar mudança para sistemas dependentes
    float NewValue = 0.0f;
    if (Attribute == GetSpectralPowerAttribute()) NewValue = SpectralPower.GetCurrentValue();
    else if (Attribute == GetSpectralResilienceAttribute()) NewValue = SpectralResilience.GetCurrentValue();
    else if (Attribute == GetSpectralVelocityAttribute()) NewValue = SpectralVelocity.GetCurrentValue();
    else if (Attribute == GetSpectralFocusAttribute()) NewValue = SpectralFocus.GetCurrentValue();
    else if (Attribute == GetAttackPowerAttribute()) NewValue = AttackPower.GetCurrentValue();
    else if (Attribute == GetDefensePowerAttribute()) NewValue = DefensePower.GetCurrentValue();
    else if (Attribute == GetAttackSpeedAttribute()) NewValue = AttackSpeed.GetCurrentValue();
    else if (Attribute == GetCriticalChanceAttribute()) NewValue = CriticalChance.GetCurrentValue();
    else if (Attribute == GetCriticalMultiplierAttribute()) NewValue = CriticalMultiplier.GetCurrentValue();
    else if (Attribute == GetMovementSpeedAttribute()) NewValue = MovementSpeed.GetCurrentValue();
    else if (Attribute == GetCooldownReductionAttribute()) NewValue = CooldownReduction.GetCurrentValue();
    else if (Attribute == GetManaRegenerationAttribute()) NewValue = ManaRegeneration.GetCurrentValue();
    else if (Attribute == GetHealthRegenerationAttribute()) NewValue = HealthRegeneration.GetCurrentValue();
    else if (Attribute == GetFusionMultiplierAttribute()) NewValue = FusionMultiplier.GetCurrentValue();
    else if (Attribute == GetSigilSlotsAttribute()) NewValue = SigilSlots.GetCurrentValue();
    else if (Attribute == GetSigilEfficiencyAttribute()) NewValue = SigilEfficiency.GetCurrentValue();
    else if (Attribute == GetSigilExperienceAttribute()) NewValue = SigilExperience.GetCurrentValue();
    else if (Attribute == GetTeamFightBonusAttribute()) NewValue = TeamFightBonus.GetCurrentValue();
    else if (Attribute == GetObjectiveBonusAttribute()) NewValue = ObjectiveBonus.GetCurrentValue();
    else if (Attribute == GetCCResistanceAttribute()) NewValue = CCResistance.GetCurrentValue();
    
    float OldValue = NewValue - Data.EvaluatedData.Magnitude;
    
    NotifyAttributeChange(Attribute, OldValue, NewValue);
}

void USigilAttributeSet::PreAttributeBaseChange(const FGameplayAttribute& Attribute, float& NewValue) const
{
    Super::PreAttributeBaseChange(Attribute, NewValue);

    // Aplicar limites finais antes de definir valor base
    ClampAttributeValue(Attribute, NewValue);
}

void USigilAttributeSet::PostAttributeChange(const FGameplayAttribute& Attribute, float OldValue, float NewValue)
{
    Super::PostAttributeChange(Attribute, OldValue, NewValue);

    // Recalcular atributos derivados se necessário
    if (Attribute == GetSpectralPowerAttribute() ||
        Attribute == GetSpectralResilienceAttribute() ||
        Attribute == GetSpectralVelocityAttribute() ||
        Attribute == GetSpectralFocusAttribute())
    {
        RecalculateDerivedAttributes();
        bTotalPowerCacheDirty = true;
    }

    // Notificar mudança
    NotifyAttributeChange(Attribute, OldValue, NewValue);
}

void USigilAttributeSet::RecalculateDerivedAttributes()
{
    // Verificação de segurança
    if (!GetOwningAbilitySystemComponent())
    {
        UE_LOG(LogSigilAttributeSet, Warning, TEXT("RecalculateDerivedAttributes: No AbilitySystemComponent found"));
        return;
    }

    // Cache dos valores primários para evitar múltiplas chamadas
    const float SpectralPowerValue = GetSpectralPower();
    const float SpectralResilienceValue = GetSpectralResilience();
    const float SpectralVelocityValue = GetSpectralVelocity();
    const float SpectralFocusValue = GetSpectralFocus();

    // Recalcular atributos derivados de combate
    float NewAttackPower = SpectralPowerValue + (SpectralPowerValue * ATTACK_POWER_MULTIPLIER);
    SetAttackPower(NewAttackPower);

    float NewDefensePower = SpectralResilienceValue + (SpectralResilienceValue * DEFENSE_POWER_MULTIPLIER);
    SetDefensePower(NewDefensePower);

    float NewAttackSpeed = BASE_ATTACK_SPEED + (SpectralVelocityValue * ATTACK_SPEED_MULTIPLIER);
    SetAttackSpeed(NewAttackSpeed);

    // Recalcular chance de crítico (baseado em dois atributos)
    float NewCritChance = (SpectralPowerValue + SpectralFocusValue) * CRIT_CHANCE_MULTIPLIER;
    NewCritChance = FMath::Clamp(NewCritChance, 0.0f, MAX_CRITICAL_CHANCE);
    SetCriticalChance(NewCritChance);

    float NewCritMultiplier = BASE_CRITICAL_MULTIPLIER + (SpectralPowerValue * CRIT_MULTIPLIER_MULTIPLIER);
    SetCriticalMultiplier(NewCritMultiplier);

    // Recalcular atributos de mobilidade
    float NewMovementSpeed = BASE_MOVEMENT_SPEED + (SpectralVelocityValue * MOVEMENT_SPEED_MULTIPLIER);
    SetMovementSpeed(NewMovementSpeed);

    float NewCDR = SpectralFocusValue * COOLDOWN_REDUCTION_MULTIPLIER;
    NewCDR = FMath::Clamp(NewCDR, 0.0f, MAX_COOLDOWN_REDUCTION);
    SetCooldownReduction(NewCDR);

    // Recalcular atributos de recursos
    float NewManaRegen = SpectralFocusValue * MANA_REGEN_MULTIPLIER;
    SetManaRegeneration(NewManaRegen);

    float NewHealthRegen = SpectralResilienceValue * HEALTH_REGEN_MULTIPLIER;
    SetHealthRegeneration(NewHealthRegen);

    // Recalcular eficiência de sigilo (fórmula específica do AURACRON)
    // Eficiência base de 100% + bônus baseado em SpectralFocus
    float NewEfficiency = 1.0f + (SpectralFocusValue * 0.005f);
    NewEfficiency = FMath::Clamp(NewEfficiency, 0.1f, 3.0f); // 10% a 300% conforme documentação
    SetSigilEfficiency(NewEfficiency);

    // Recalcular resistência a CC (fórmula específica do AURACRON)
    float NewCCResistance = SpectralResilienceValue * 0.002f;
    NewCCResistance = FMath::Clamp(NewCCResistance, 0.0f, MAX_CC_RESISTANCE);
    SetCCResistance(NewCCResistance);

    // Marcar cache como sujo
    bTotalPowerCacheDirty = true;

    // Log detalhado para debugging
    UE_LOG(LogSigilAttributeSet, VeryVerbose, TEXT("RecalculateDerivedAttributes completed for %s"),
           *GetOwningAbilitySystemComponent()->GetOwner()->GetName());
}

void USigilAttributeSet::ApplyFusionMultiplier(float Multiplier)
{
    // Verificações de segurança
    if (!GetOwningAbilitySystemComponent())
    {
        UE_LOG(LogSigilAttributeSet, Warning, TEXT("ApplyFusionMultiplier: No AbilitySystemComponent found"));
        return;
    }

    // Validar multiplicador dentro de limites aceitáveis (1.0 a 3.0 conforme documentação)
    const float ClampedMultiplier = FMath::Clamp(Multiplier, 1.0f, 3.0f);
    if (ClampedMultiplier != Multiplier)
    {
        UE_LOG(LogSigilAttributeSet, Warning, TEXT("ApplyFusionMultiplier: Multiplier %f clamped to %f"),
               Multiplier, ClampedMultiplier);
    }

    // Verificar se já há um multiplicador ativo
    const float CurrentMultiplier = GetFusionMultiplier();
    if (CurrentMultiplier > 1.0f)
    {
        UE_LOG(LogSigilAttributeSet, Warning, TEXT("ApplyFusionMultiplier: Overriding existing multiplier %f with %f"),
               CurrentMultiplier, ClampedMultiplier);
        // Remover multiplicador anterior primeiro
        RemoveFusionMultiplier();
    }

    SetFusionMultiplier(ClampedMultiplier);

    // Aplicar multiplicador aos atributos de combate usando valores atuais
    const float CurrentAttackPower = GetAttackPower();
    const float CurrentDefensePower = GetDefensePower();
    const float CurrentAttackSpeed = GetAttackSpeed();

    SetAttackPower(CurrentAttackPower * ClampedMultiplier);
    SetDefensePower(CurrentDefensePower * ClampedMultiplier);
    SetAttackSpeed(CurrentAttackSpeed * ClampedMultiplier);

    // Marcar cache como sujo
    bTotalPowerCacheDirty = true;

    // Log para debugging com categoria específica
    UE_LOG(LogSigilAttributeSet, Log, TEXT("Fusion Multiplier Applied: %f to %s (Attack: %f->%f, Defense: %f->%f, Speed: %f->%f)"),
           ClampedMultiplier, *GetOwningAbilitySystemComponent()->GetOwner()->GetName(),
           CurrentAttackPower, GetAttackPower(), CurrentDefensePower, GetDefensePower(),
           CurrentAttackSpeed, GetAttackSpeed());
}

void USigilAttributeSet::RemoveFusionMultiplier()
{
    // Verificações de segurança
    if (!GetOwningAbilitySystemComponent())
    {
        UE_LOG(LogSigilAttributeSet, Warning, TEXT("RemoveFusionMultiplier: No AbilitySystemComponent found"));
        return;
    }

    const float CurrentMultiplier = GetFusionMultiplier();
    if (CurrentMultiplier > 1.0f)
    {
        // Verificação de segurança para divisão
        if (CurrentMultiplier < KINDA_SMALL_NUMBER)
        {
            UE_LOG(LogSigilAttributeSet, Error, TEXT("RemoveFusionMultiplier: Invalid multiplier value %f"), CurrentMultiplier);
            SetFusionMultiplier(1.0f);
            return;
        }

        const float InverseMultiplier = 1.0f / CurrentMultiplier;

        // Capturar valores atuais para logging
        const float CurrentAttackPower = GetAttackPower();
        const float CurrentDefensePower = GetDefensePower();
        const float CurrentAttackSpeed = GetAttackSpeed();

        // Remover multiplicador dos atributos de combate
        SetAttackPower(CurrentAttackPower * InverseMultiplier);
        SetDefensePower(CurrentDefensePower * InverseMultiplier);
        SetAttackSpeed(CurrentAttackSpeed * InverseMultiplier);

        SetFusionMultiplier(1.0f);
        bTotalPowerCacheDirty = true;

        UE_LOG(LogSigilAttributeSet, Log, TEXT("Fusion Multiplier Removed from %s (Multiplier: %f, Attack: %f->%f, Defense: %f->%f, Speed: %f->%f)"),
               *GetOwningAbilitySystemComponent()->GetOwner()->GetName(), CurrentMultiplier,
               CurrentAttackPower, GetAttackPower(), CurrentDefensePower, GetDefensePower(),
               CurrentAttackSpeed, GetAttackSpeed());
    }
    else
    {
        UE_LOG(LogSigilAttributeSet, VeryVerbose, TEXT("RemoveFusionMultiplier: No active multiplier to remove (current: %f)"), CurrentMultiplier);
    }
}

float USigilAttributeSet::CalculateTotalSigilPower() const
{
    if (bTotalPowerCacheDirty)
    {
        // Verificação de segurança
        if (!GetOwningAbilitySystemComponent())
        {
            UE_LOG(LogSigilAttributeSet, Warning, TEXT("CalculateTotalSigilPower: No AbilitySystemComponent found"));
            CachedTotalPower = 0.0f;
            bTotalPowerCacheDirty = false;
            return CachedTotalPower;
        }

        // Calcular poder total como soma ponderada dos atributos espectrais
        // Pesos baseados na documentação do AURACRON: Power > Velocity > Resilience > Focus
        const float SpectralPowerContribution = GetSpectralPower() * 1.0f;
        const float SpectralResilienceContribution = GetSpectralResilience() * 0.8f;
        const float SpectralVelocityContribution = GetSpectralVelocity() * 0.9f;
        const float SpectralFocusContribution = GetSpectralFocus() * 0.7f;

        CachedTotalPower = SpectralPowerContribution + SpectralResilienceContribution +
                          SpectralVelocityContribution + SpectralFocusContribution;

        // Aplicar multiplicador de fusão (validado)
        const float FusionMult = FMath::Clamp(GetFusionMultiplier(), 1.0f, 3.0f);
        CachedTotalPower *= FusionMult;

        // Aplicar eficiência de sigilo (validada conforme documentação AURACRON)
        const float SigilEff = FMath::Clamp(GetSigilEfficiency(), 0.1f, 3.0f);
        CachedTotalPower *= SigilEff;

        // Garantir que o valor final seja positivo
        CachedTotalPower = FMath::Max(CachedTotalPower, 0.0f);

        bTotalPowerCacheDirty = false;

        UE_LOG(LogSigilAttributeSet, VeryVerbose, TEXT("CalculateTotalSigilPower: %f (Power:%f, Resilience:%f, Velocity:%f, Focus:%f, Fusion:%f, Efficiency:%f)"),
               CachedTotalPower, SpectralPowerContribution, SpectralResilienceContribution,
               SpectralVelocityContribution, SpectralFocusContribution, FusionMult, SigilEff);
    }

    return CachedTotalPower;
}

bool USigilAttributeSet::IsAttributeAtMaximum(const FGameplayAttribute& Attribute) const
{
    float CurrentValue = 0.0f;
    if (Attribute == GetSpectralPowerAttribute()) CurrentValue = SpectralPower.GetCurrentValue();
    else if (Attribute == GetSpectralResilienceAttribute()) CurrentValue = SpectralResilience.GetCurrentValue();
    else if (Attribute == GetSpectralVelocityAttribute()) CurrentValue = SpectralVelocity.GetCurrentValue();
    else if (Attribute == GetSpectralFocusAttribute()) CurrentValue = SpectralFocus.GetCurrentValue();
    else if (Attribute == GetAttackPowerAttribute()) CurrentValue = AttackPower.GetCurrentValue();
    else if (Attribute == GetDefensePowerAttribute()) CurrentValue = DefensePower.GetCurrentValue();
    else if (Attribute == GetAttackSpeedAttribute()) CurrentValue = AttackSpeed.GetCurrentValue();
    else if (Attribute == GetCriticalChanceAttribute()) CurrentValue = CriticalChance.GetCurrentValue();
    else if (Attribute == GetCriticalMultiplierAttribute()) CurrentValue = CriticalMultiplier.GetCurrentValue();
    else if (Attribute == GetMovementSpeedAttribute()) CurrentValue = MovementSpeed.GetCurrentValue();
    else if (Attribute == GetCooldownReductionAttribute()) CurrentValue = CooldownReduction.GetCurrentValue();
    else if (Attribute == GetManaRegenerationAttribute()) CurrentValue = ManaRegeneration.GetCurrentValue();
    else if (Attribute == GetHealthRegenerationAttribute()) CurrentValue = HealthRegeneration.GetCurrentValue();
    else if (Attribute == GetFusionMultiplierAttribute()) CurrentValue = FusionMultiplier.GetCurrentValue();
    else if (Attribute == GetSigilSlotsAttribute()) CurrentValue = SigilSlots.GetCurrentValue();
    else if (Attribute == GetSigilEfficiencyAttribute()) CurrentValue = SigilEfficiency.GetCurrentValue();
    else if (Attribute == GetSigilExperienceAttribute()) CurrentValue = SigilExperience.GetCurrentValue();
    else if (Attribute == GetTeamFightBonusAttribute()) CurrentValue = TeamFightBonus.GetCurrentValue();
    else if (Attribute == GetObjectiveBonusAttribute()) CurrentValue = ObjectiveBonus.GetCurrentValue();
    else if (Attribute == GetCCResistanceAttribute()) CurrentValue = CCResistance.GetCurrentValue();
    
    // Verificar limites específicos por atributo
    if (Attribute == GetCooldownReductionAttribute())
    {
        return CurrentValue >= MAX_COOLDOWN_REDUCTION;
    }
    else if (Attribute == GetCriticalChanceAttribute())
    {
        return CurrentValue >= MAX_CRITICAL_CHANCE;
    }
    else if (Attribute == GetCCResistanceAttribute())
    {
        return CurrentValue >= MAX_CC_RESISTANCE;
    }
    else if (Attribute == GetSigilSlotsAttribute())
    {
        return CurrentValue >= MAX_SIGIL_SLOTS;
    }
    
    // Para outros atributos, assumir que não há máximo fixo
    return false;
}

float USigilAttributeSet::GetEffectiveAttributeValue(const FGameplayAttribute& Attribute) const
{
    float BaseValue = 0.0f;
    if (Attribute == GetSpectralPowerAttribute()) BaseValue = SpectralPower.GetCurrentValue();
    else if (Attribute == GetSpectralResilienceAttribute()) BaseValue = SpectralResilience.GetCurrentValue();
    else if (Attribute == GetSpectralVelocityAttribute()) BaseValue = SpectralVelocity.GetCurrentValue();
    else if (Attribute == GetSpectralFocusAttribute()) BaseValue = SpectralFocus.GetCurrentValue();
    else if (Attribute == GetAttackPowerAttribute()) BaseValue = AttackPower.GetCurrentValue();
    else if (Attribute == GetDefensePowerAttribute()) BaseValue = DefensePower.GetCurrentValue();
    else if (Attribute == GetAttackSpeedAttribute()) BaseValue = AttackSpeed.GetCurrentValue();
    else if (Attribute == GetCriticalChanceAttribute()) BaseValue = CriticalChance.GetCurrentValue();
    else if (Attribute == GetCriticalMultiplierAttribute()) BaseValue = CriticalMultiplier.GetCurrentValue();
    else if (Attribute == GetMovementSpeedAttribute()) BaseValue = MovementSpeed.GetCurrentValue();
    else if (Attribute == GetCooldownReductionAttribute()) BaseValue = CooldownReduction.GetCurrentValue();
    else if (Attribute == GetManaRegenerationAttribute()) BaseValue = ManaRegeneration.GetCurrentValue();
    else if (Attribute == GetHealthRegenerationAttribute()) BaseValue = HealthRegeneration.GetCurrentValue();
    else if (Attribute == GetFusionMultiplierAttribute()) BaseValue = FusionMultiplier.GetCurrentValue();
    else if (Attribute == GetSigilSlotsAttribute()) BaseValue = SigilSlots.GetCurrentValue();
    else if (Attribute == GetSigilEfficiencyAttribute()) BaseValue = SigilEfficiency.GetCurrentValue();
    else if (Attribute == GetSigilExperienceAttribute()) BaseValue = SigilExperience.GetCurrentValue();
    else if (Attribute == GetTeamFightBonusAttribute()) BaseValue = TeamFightBonus.GetCurrentValue();
    else if (Attribute == GetObjectiveBonusAttribute()) BaseValue = ObjectiveBonus.GetCurrentValue();
    else if (Attribute == GetCCResistanceAttribute()) BaseValue = CCResistance.GetCurrentValue();
    
    // Aplicar multiplicadores relevantes
    if (Attribute == GetAttackPowerAttribute() ||
        Attribute == GetDefensePowerAttribute() ||
        Attribute == GetAttackSpeedAttribute())
    {
        BaseValue *= GetFusionMultiplier();
    }
    
    // Aplicar eficiência de sigilo para atributos espectrais
    if (Attribute == GetSpectralPowerAttribute() ||
        Attribute == GetSpectralResilienceAttribute() ||
        Attribute == GetSpectralVelocityAttribute() ||
        Attribute == GetSpectralFocusAttribute())
    {
        BaseValue *= GetSigilEfficiency();
    }
    
    return BaseValue;
}

// ========================================
// FUNÇÕES DE REPLICAÇÃO - ATRIBUTOS PRIMÁRIOS
// ========================================

void USigilAttributeSet::OnRep_SpectralPower(const FGameplayAttributeData& OldSpectralPower)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAttributeSet, SpectralPower, OldSpectralPower);
    RecalculateDerivedAttributes();
}

void USigilAttributeSet::OnRep_SpectralResilience(const FGameplayAttributeData& OldSpectralResilience)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAttributeSet, SpectralResilience, OldSpectralResilience);
    RecalculateDerivedAttributes();
}

void USigilAttributeSet::OnRep_SpectralVelocity(const FGameplayAttributeData& OldSpectralVelocity)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAttributeSet, SpectralVelocity, OldSpectralVelocity);
    RecalculateDerivedAttributes();
}

void USigilAttributeSet::OnRep_SpectralFocus(const FGameplayAttributeData& OldSpectralFocus)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAttributeSet, SpectralFocus, OldSpectralFocus);
    RecalculateDerivedAttributes();
}

// ========================================
// FUNÇÕES DE REPLICAÇÃO - ATRIBUTOS DERIVADOS
// ========================================

void USigilAttributeSet::OnRep_AttackPower(const FGameplayAttributeData& OldAttackPower)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAttributeSet, AttackPower, OldAttackPower);
}

void USigilAttributeSet::OnRep_DefensePower(const FGameplayAttributeData& OldDefensePower)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAttributeSet, DefensePower, OldDefensePower);
}

void USigilAttributeSet::OnRep_AttackSpeed(const FGameplayAttributeData& OldAttackSpeed)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAttributeSet, AttackSpeed, OldAttackSpeed);
}

void USigilAttributeSet::OnRep_CriticalChance(const FGameplayAttributeData& OldCriticalChance)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAttributeSet, CriticalChance, OldCriticalChance);
}

void USigilAttributeSet::OnRep_CriticalMultiplier(const FGameplayAttributeData& OldCriticalMultiplier)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAttributeSet, CriticalMultiplier, OldCriticalMultiplier);
}

// ========================================
// FUNÇÕES DE REPLICAÇÃO - MOBILIDADE
// ========================================

void USigilAttributeSet::OnRep_MovementSpeed(const FGameplayAttributeData& OldMovementSpeed)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAttributeSet, MovementSpeed, OldMovementSpeed);
}

void USigilAttributeSet::OnRep_CooldownReduction(const FGameplayAttributeData& OldCooldownReduction)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAttributeSet, CooldownReduction, OldCooldownReduction);
}

// ========================================
// FUNÇÕES DE REPLICAÇÃO - RECURSOS
// ========================================

void USigilAttributeSet::OnRep_ManaRegeneration(const FGameplayAttributeData& OldManaRegeneration)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAttributeSet, ManaRegeneration, OldManaRegeneration);
}

void USigilAttributeSet::OnRep_HealthRegeneration(const FGameplayAttributeData& OldHealthRegeneration)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAttributeSet, HealthRegeneration, OldHealthRegeneration);
}

// ========================================
// FUNÇÕES DE REPLICAÇÃO - ESTADO
// ========================================

void USigilAttributeSet::OnRep_FusionMultiplier(const FGameplayAttributeData& OldFusionMultiplier)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAttributeSet, FusionMultiplier, OldFusionMultiplier);
    bTotalPowerCacheDirty = true;
}

void USigilAttributeSet::OnRep_SigilSlots(const FGameplayAttributeData& OldSigilSlots)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAttributeSet, SigilSlots, OldSigilSlots);
}

void USigilAttributeSet::OnRep_SigilEfficiency(const FGameplayAttributeData& OldSigilEfficiency)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAttributeSet, SigilEfficiency, OldSigilEfficiency);
    bTotalPowerCacheDirty = true;
}

void USigilAttributeSet::OnRep_SigilExperience(const FGameplayAttributeData& OldSigilExperience)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAttributeSet, SigilExperience, OldSigilExperience);
}

// ========================================
// FUNÇÕES DE REPLICAÇÃO - MOBA
// ========================================

void USigilAttributeSet::OnRep_TeamFightBonus(const FGameplayAttributeData& OldTeamFightBonus)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAttributeSet, TeamFightBonus, OldTeamFightBonus);
}

void USigilAttributeSet::OnRep_ObjectiveBonus(const FGameplayAttributeData& OldObjectiveBonus)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAttributeSet, ObjectiveBonus, OldObjectiveBonus);
}

void USigilAttributeSet::OnRep_CCResistance(const FGameplayAttributeData& OldCCResistance)
{
    GAMEPLAYATTRIBUTE_REPNOTIFY(USigilAttributeSet, CCResistance, OldCCResistance);
}

// ========================================
// FUNÇÕES INTERNAS PRIVADAS
// ========================================

void USigilAttributeSet::AdjustAttributeForMaxChange(const FGameplayAttributeData& AffectedAttribute,
                                                    const FGameplayAttributeData& MaxAttribute,
                                                    float NewMaxValue,
                                                    const FGameplayAttribute& AffectedAttributeProperty) const
{
    UAbilitySystemComponent* AbilityComp = GetOwningAbilitySystemComponent();
    if (!AbilityComp)
    {
        UE_LOG(LogSigilAttributeSet, Warning, TEXT("AdjustAttributeForMaxChange: No AbilitySystemComponent found"));
        return;
    }

    const float CurrentMaxValue = MaxAttribute.GetCurrentValue();
    if (NewMaxValue != CurrentMaxValue)
    {
        const float CurrentValue = AffectedAttribute.GetCurrentValue();

        // Verificação de segurança para divisão por zero
        float NewDelta = 0.0f;
        if (CurrentMaxValue > KINDA_SMALL_NUMBER)
        {
            NewDelta = (CurrentValue * NewMaxValue / CurrentMaxValue) - CurrentValue;
        }
        else
        {
            NewDelta = NewMaxValue;
        }

        AbilityComp->ApplyModToAttributeUnsafe(AffectedAttributeProperty, EGameplayModOp::Additive, NewDelta);

        UE_LOG(LogSigilAttributeSet, VeryVerbose, TEXT("AdjustAttributeForMaxChange: %s adjusted by %f"),
               *AffectedAttributeProperty.GetName(), NewDelta);
    }
}

float USigilAttributeSet::CalculateDerivedValue(const FGameplayAttribute& PrimaryAttribute,
                                              float Multiplier,
                                              float BaseValue) const
{
    float PrimaryValue = 0.0f;
    if (PrimaryAttribute == GetSpectralPowerAttribute()) PrimaryValue = SpectralPower.GetCurrentValue();
    else if (PrimaryAttribute == GetSpectralResilienceAttribute()) PrimaryValue = SpectralResilience.GetCurrentValue();
    else if (PrimaryAttribute == GetSpectralVelocityAttribute()) PrimaryValue = SpectralVelocity.GetCurrentValue();
    else if (PrimaryAttribute == GetSpectralFocusAttribute()) PrimaryValue = SpectralFocus.GetCurrentValue();
    else if (PrimaryAttribute == GetAttackPowerAttribute()) PrimaryValue = AttackPower.GetCurrentValue();
    else if (PrimaryAttribute == GetDefensePowerAttribute()) PrimaryValue = DefensePower.GetCurrentValue();
    else if (PrimaryAttribute == GetAttackSpeedAttribute()) PrimaryValue = AttackSpeed.GetCurrentValue();
    else if (PrimaryAttribute == GetCriticalChanceAttribute()) PrimaryValue = CriticalChance.GetCurrentValue();
    else if (PrimaryAttribute == GetCriticalMultiplierAttribute()) PrimaryValue = CriticalMultiplier.GetCurrentValue();
    else if (PrimaryAttribute == GetMovementSpeedAttribute()) PrimaryValue = MovementSpeed.GetCurrentValue();
    else if (PrimaryAttribute == GetCooldownReductionAttribute()) PrimaryValue = CooldownReduction.GetCurrentValue();
    else if (PrimaryAttribute == GetManaRegenerationAttribute()) PrimaryValue = ManaRegeneration.GetCurrentValue();
    else if (PrimaryAttribute == GetHealthRegenerationAttribute()) PrimaryValue = HealthRegeneration.GetCurrentValue();
    else if (PrimaryAttribute == GetFusionMultiplierAttribute()) PrimaryValue = FusionMultiplier.GetCurrentValue();
    else if (PrimaryAttribute == GetSigilSlotsAttribute()) PrimaryValue = SigilSlots.GetCurrentValue();
    else if (PrimaryAttribute == GetSigilEfficiencyAttribute()) PrimaryValue = SigilEfficiency.GetCurrentValue();
    else if (PrimaryAttribute == GetSigilExperienceAttribute()) PrimaryValue = SigilExperience.GetCurrentValue();
    else if (PrimaryAttribute == GetTeamFightBonusAttribute()) PrimaryValue = TeamFightBonus.GetCurrentValue();
    else if (PrimaryAttribute == GetObjectiveBonusAttribute()) PrimaryValue = ObjectiveBonus.GetCurrentValue();
    else if (PrimaryAttribute == GetCCResistanceAttribute()) PrimaryValue = CCResistance.GetCurrentValue();
    return BaseValue + (PrimaryValue * Multiplier);
}

void USigilAttributeSet::ClampAttributeValue(const FGameplayAttribute& Attribute, float& Value) const
{
    // Aplicar limites mínimos (todos os atributos >= 0)
    Value = FMath::Max(Value, 0.0f);

    // Aplicar limites máximos específicos
    if (Attribute == GetCooldownReductionAttribute())
    {
        Value = FMath::Min(Value, MAX_COOLDOWN_REDUCTION);
    }
    else if (Attribute == GetCriticalChanceAttribute())
    {
        Value = FMath::Min(Value, MAX_CRITICAL_CHANCE);
    }
    else if (Attribute == GetCCResistanceAttribute())
    {
        Value = FMath::Min(Value, MAX_CC_RESISTANCE);
    }
    else if (Attribute == GetSigilSlotsAttribute())
    {
        Value = FMath::Min(Value, static_cast<float>(MAX_SIGIL_SLOTS));
    }
    else if (Attribute == GetFusionMultiplierAttribute())
    {
        // Multiplicador de fusão entre 1.0 e 3.0
        Value = FMath::Clamp(Value, 1.0f, 3.0f);
    }
    else if (Attribute == GetSigilEfficiencyAttribute())
    {
        // Eficiência entre 10% e 300% conforme documentação AURACRON
        // Convertendo para valores decimais: 0.1f a 3.0f
        Value = FMath::Clamp(Value, 0.1f, 3.0f);
    }
}

void USigilAttributeSet::NotifyAttributeChange(const FGameplayAttribute& Attribute, float OldValue, float NewValue)
{
    // Broadcast para sistemas que precisam saber sobre mudanças de atributos
    UAbilitySystemComponent* AbilityComp = GetOwningAbilitySystemComponent();
    if (!AbilityComp)
    {
        UE_LOG(LogSigilAttributeSet, Warning, TEXT("NotifyAttributeChange: No AbilitySystemComponent found"));
        return;
    }

    // Notificar mudança através do AbilitySystemComponent
    // Outros sistemas podem se inscrever para essas notificações

    // Verificar se a mudança é significativa antes de logar
    const float DeltaThreshold = 0.001f;
    if (FMath::Abs(NewValue - OldValue) > DeltaThreshold)
    {
        // Log para debugging com categoria específica
        if (GEngine && GEngine->GetNetMode(GetWorld()) != NM_DedicatedServer)
        {
            FString AttributeName = Attribute.GetName();
            FString OwnerName = AbilityComp->GetOwner() ? AbilityComp->GetOwner()->GetName() : TEXT("Unknown");
            UE_LOG(LogSigilAttributeSet, VeryVerbose, TEXT("Sigil Attribute Changed: %s from %f to %f on %s"),
                   *AttributeName, OldValue, NewValue, *OwnerName);
        }

        // Broadcast para sistemas externos se necessário
        // Pode ser expandido para incluir delegates específicos do projeto
    }
}